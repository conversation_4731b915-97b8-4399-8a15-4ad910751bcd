// ─── Sample Coverages for <PERSON><PERSON> ──────────────────────────────
export const sampleCoverages = [
  {
    type: "MANDATORY_LIABILITY",
    description: "Responsabilidad civil obligatoria",
    limit: null,
    limitIsUnlimited: false,
    limitIsFullCost: false,
    limitPerDay: null,
    limitMaxDays: null,
    limitMaxMonths: null,
    liabilityBodilyCap: 70000000,
    liabilityPropertyCap: 15000000,
    deductible: null,
    deductiblePercent: null,
    customName: null
  },
  {
    type: "VOLUNTARY_LIABILITY",
    description: "Responsabilidad civil voluntaria",
    limit: 50000000,
    limitIsUnlimited: false,
    limitIsFullCost: false,
    limitPerDay: null,
    limitMaxDays: null,
    limitMaxMonths: null,
    liabilityBodilyCap: null,
    liabilityPropertyCap: null,
    deductible: null,
    deductiblePercent: null,
    customName: null
  },
  {
    type: "LEGAL_DEFENSE",
    description: "Defensa jurídica ilimitada con abogado de la compañía",
    limit: null,
    limitIsUnlimited: true,
    limitIsFullCost: false,
    limitPerDay: null,
    limitMaxDays: null,
    limitMaxMonths: null,
    liabilityBodilyCap: null,
    liabilityPropertyCap: null,
    deductible: null,
    deductiblePercent: null,
    customName: null
  },
  {
    type: "LEGAL_DEFENSE",
    description: "Defensa jurídica libre elección",
    limit: 1000,
    limitIsUnlimited: false,
    limitIsFullCost: false,
    limitPerDay: null,
    limitMaxDays: null,
    limitMaxMonths: null,
    liabilityBodilyCap: null,
    liabilityPropertyCap: null,
    deductible: null,
    deductiblePercent: null,
    customName: null
  },
  {
    type: "TRAVEL_ASSISTANCE",
    description: "Remolque del vehículo desde km 0",
    limit: null,
    limitIsUnlimited: true,
    limitIsFullCost: false,
    limitPerDay: null,
    limitMaxDays: null,
    limitMaxMonths: null,
    liabilityBodilyCap: null,
    liabilityPropertyCap: null,
    deductible: null,
    deductiblePercent: null,
    customName: null
  },
  {
    type: "HOTEL_EXPENSES_TRAVEL_ASSIST",
    description: "Gastos de hotel por inmovilización en viaje",
    limit: null,
    limitIsUnlimited: false,
    limitIsFullCost: false,
    limitPerDay: 80,
    limitMaxDays: 10,
    limitMaxMonths: null,
    liabilityBodilyCap: null,
    liabilityPropertyCap: null,
    deductible: null,
    deductiblePercent: null,
    customName: null
  },
  {
    type: "RESCUE_EXPENSES",
    description: "Gastos de rescate y custodia",
    limit: 180,
    limitIsUnlimited: false,
    limitIsFullCost: false,
    limitPerDay: null,
    limitMaxDays: null,
    limitMaxMonths: null,
    liabilityBodilyCap: null,
    liabilityPropertyCap: null,
    deductible: null,
    deductiblePercent: null,
    customName: null
  },
  {
    type: "VEHICLE_DAMAGE",
    description: "Daños propios con franquicia mixta",
    limit: null,
    limitIsUnlimited: false,
    limitIsFullCost: false,
    limitPerDay: null,
    limitMaxDays: null,
    limitMaxMonths: null,
    liabilityBodilyCap: null,
    liabilityPropertyCap: null,
    deductible: 100,
    deductiblePercent: 0.2,
    customName: null
  },
  {
    type: "GLASS_BREAKAGE",
    description: "Sustitución de lunas sin límite",
    limit: null,
    limitIsUnlimited: false,
    limitIsFullCost: true,
    limitPerDay: null,
    limitMaxDays: null,
    limitMaxMonths: null,
    liabilityBodilyCap: null,
    liabilityPropertyCap: null,
    deductible: null,
    deductiblePercent: null,
    customName: null
  },
  {
    type: "NEW_VALUE_COMPENSATION",
    description:
      "Valor a nuevo 2 años, 80% en el año 3, resto valor de mercado",
    limit: null,
    limitIsUnlimited: false,
    limitIsFullCost: false,
    limitPerDay: null,
    limitMaxDays: null,
    limitMaxMonths: null,
    liabilityBodilyCap: null,
    liabilityPropertyCap: null,
    deductible: null,
    deductiblePercent: null,
    customName: null
  },
  {
    type: "LICENSE_SUSPENSION_SUBSIDY",
    description: "Subsidio diario por retirada del permiso",
    limit: null,
    limitIsUnlimited: false,
    limitIsFullCost: false,
    limitPerDay: 37,
    limitMaxDays: null,
    limitMaxMonths: 3,
    liabilityBodilyCap: null,
    liabilityPropertyCap: null,
    deductible: null,
    deductiblePercent: null,
    customName: null
  },
  {
    type: "GAP_COVERAGE",
    description: "Cobertura GAP para financiación o leasing",
    limit: 20000,
    limitIsUnlimited: false,
    limitIsFullCost: false,
    limitPerDay: null,
    limitMaxDays: null,
    limitMaxMonths: null,
    liabilityBodilyCap: null,
    liabilityPropertyCap: null,
    deductible: null,
    deductiblePercent: null,
    customName: null
  },
  {
    type: "FREE_WORKSHOP_CHOICE",
    description: "Libre elección de taller",
    limit: null,
    limitIsUnlimited: false,
    limitIsFullCost: false,
    limitPerDay: null,
    limitMaxDays: null,
    limitMaxMonths: null,
    liabilityBodilyCap: null,
    liabilityPropertyCap: null,
    deductible: null,
    deductiblePercent: null,
    customName: null
  },
  {
    type: "PET_INJURY",
    description: "Cobertura por lesiones a mascotas en accidente",
    limit: 1500,
    limitIsUnlimited: false,
    limitIsFullCost: false,
    limitPerDay: null,
    limitMaxDays: null,
    limitMaxMonths: null,
    liabilityBodilyCap: null,
    liabilityPropertyCap: null,
    deductible: null,
    deductiblePercent: null,
    customName: null
  },
  {
    type: "OTHER",
    customName: "Intereses y gastos de financiación en reparación",
    description: "Cobertura de cargos financieros",
    limit: null,
    limitIsUnlimited: false,
    limitIsFullCost: false,
    limitPerDay: null,
    limitMaxDays: null,
    limitMaxMonths: null,
    liabilityBodilyCap: null,
    liabilityPropertyCap: null,
    deductible: null,
    deductiblePercent: null
  },
  {
    type: "MANDATORY_LIABILITY",
    description: "Responsabilidad civil obligatoria",
    limit: null,
    limitIsUnlimited: false,
    limitIsFullCost: false,
    limitPerDay: null,
    limitMaxDays: null,
    limitMaxMonths: null,
    liabilityBodilyCap: 70000000,
    liabilityPropertyCap: 15000000,
    deductible: null,
    deductiblePercent: null,
    customName: null
  },
  {
    type: "VOLUNTARY_LIABILITY",
    description: "RC complementaria",
    limit: 50000000,
    limitIsUnlimited: false,
    limitIsFullCost: false,
    limitPerDay: null,
    limitMaxDays: null,
    limitMaxMonths: null,
    liabilityBodilyCap: null,
    liabilityPropertyCap: null,
    deductible: null,
    deductiblePercent: null,
    customName: null
  },
  {
    type: "VOLUNTARY_LIABILITY",
    description: "RC del conductor/tomador como peatón y ciclista",
    limit: 100000,
    limitIsUnlimited: false,
    limitIsFullCost: false,
    limitPerDay: null,
    limitMaxDays: null,
    limitMaxMonths: null,
    liabilityBodilyCap: null,
    liabilityPropertyCap: null,
    deductible: null,
    deductiblePercent: null,
    customName: null
  },
  {
    type: "VOLUNTARY_LIABILITY",
    description:
      "RC por incendio o explosión del vehículo asegurado (estacionado)",
    limit: 100000,
    limitIsUnlimited: false,
    limitIsFullCost: false,
    limitPerDay: null,
    limitMaxDays: null,
    limitMaxMonths: null,
    liabilityBodilyCap: null,
    liabilityPropertyCap: null,
    deductible: null,
    deductiblePercent: null,
    customName: null
  },

  {
    type: "LEGAL_DEFENSE",
    description: "Protección jurídica (límite por siniestro)",
    limit: 1000,
    limitIsUnlimited: false,
    limitIsFullCost: false,
    limitPerDay: null,
    limitMaxDays: null,
    limitMaxMonths: null,
    liabilityBodilyCap: null,
    liabilityPropertyCap: null,
    deductible: null,
    deductiblePercent: null,
    customName: null
  },
  {
    type: "LEGAL_DEFENSE",
    description: "Defensa judicial (límite por siniestro)",
    limit: 1000,
    limitIsUnlimited: false,
    limitIsFullCost: false,
    limitPerDay: null,
    limitMaxDays: null,
    limitMaxMonths: null,
    liabilityBodilyCap: null,
    liabilityPropertyCap: null,
    deductible: null,
    deductiblePercent: null,
    customName: null
  },
  {
    type: "LEGAL_DEFENSE",
    description: "Reclamación de daños (límite por siniestro)",
    limit: 1000,
    limitIsUnlimited: false,
    limitIsFullCost: false,
    limitPerDay: null,
    limitMaxDays: null,
    limitMaxMonths: null,
    liabilityBodilyCap: null,
    liabilityPropertyCap: null,
    deductible: null,
    deductiblePercent: null,
    customName: null
  },
  {
    type: "FINES_MANAGEMENT",
    description: "Defensa de multas (gestión administrativa)",
    limit: null,
    limitIsUnlimited: false,
    limitIsFullCost: false,
    limitPerDay: null,
    limitMaxDays: null,
    limitMaxMonths: null,
    liabilityBodilyCap: null,
    liabilityPropertyCap: null,
    deductible: null,
    deductiblePercent: null,
    customName: null
  },

  {
    type: "ADVANCE_COMPENSATION",
    description:
      "Adelanto del importe de reparación del vehículo por daños materiales",
    limit: 10000,
    limitIsUnlimited: false,
    limitIsFullCost: false,
    limitPerDay: null,
    limitMaxDays: null,
    limitMaxMonths: null,
    liabilityBodilyCap: null,
    liabilityPropertyCap: null,
    deductible: null,
    deductiblePercent: null,
    customName: null
  },

  {
    type: "GLASS_BREAKAGE",
    description: "Rotura de lunas (incluye ADAS y techo)",
    limit: null,
    limitIsUnlimited: false,
    limitIsFullCost: true,
    limitPerDay: null,
    limitMaxDays: null,
    limitMaxMonths: null,
    liabilityBodilyCap: null,
    liabilityPropertyCap: null,
    deductible: null,
    deductiblePercent: null,
    customName: null
  },
  {
    type: "WEATHER_DAMAGE",
    description: "Daños por fenómenos atmosféricos (incluida)",
    limit: null,
    limitIsUnlimited: false,
    limitIsFullCost: false,
    limitPerDay: null,
    limitMaxDays: null,
    limitMaxMonths: null,
    liabilityBodilyCap: null,
    liabilityPropertyCap: null,
    deductible: null,
    deductiblePercent: null,
    customName: null
  },
  {
    type: "COLLISION_WITH_ANIMALS",
    description: "Daños por animales cinegéticos/domésticos",
    limit: 12000,
    limitIsUnlimited: false,
    limitIsFullCost: false,
    limitPerDay: null,
    limitMaxDays: null,
    limitMaxMonths: null,
    liabilityBodilyCap: null,
    liabilityPropertyCap: null,
    deductible: null,
    deductiblePercent: null,
    customName: null
  },
  {
    type: "FIRE",
    description: "Incendio (incluida)",
    limit: null,
    limitIsUnlimited: false,
    limitIsFullCost: true,
    limitPerDay: null,
    limitMaxDays: null,
    limitMaxMonths: null,
    liabilityBodilyCap: null,
    liabilityPropertyCap: null,
    deductible: null,
    deductiblePercent: null,
    customName: null
  },
  {
    type: "THEFT",
    description: "Robo (incluida)",
    limit: null,
    limitIsUnlimited: false,
    limitIsFullCost: true,
    limitPerDay: null,
    limitMaxDays: null,
    limitMaxMonths: null,
    liabilityBodilyCap: null,
    liabilityPropertyCap: null,
    deductible: null,
    deductiblePercent: null,
    customName: null
  },
  {
    type: "OTHER",
    customName: "Reparaciones urgentes",
    description: "Reparaciones urgentes por siniestro",
    limit: 200,
  },
  {
    type: "NEW_VALUE_COMPENSATION",
    description:
      "100% valor nuevo < 2 años (nuevo) o valor compra < 2 años (usado); 80% año 3; resto valor de mercado",
    limitMaxMonths: 24,
  },

  {
    type: "DRIVER_ACCIDENTS",
    description: "Capital en caso de muerte",
    limit: 15000,
  },
  {
    type: "DRIVER_ACCIDENTS",
    description: "Capital en caso de invalidez",
    limit: 15000,
  },
  {
    type: "DRIVER_ACCIDENTS",
    description: "Asistencia sanitaria en centros concertados (ilimitada)",
    limitIsUnlimited: true,
  },
  {
    type: "DRIVER_ACCIDENTS",
    description: "Asistencia sanitaria en libre elección",
    limit: 1500,
  },
  {
    type: "ASSISTIVE_EQUIPMENT_RENTAL",
    description: "Alquiler de sillas de ruedas o muletas",
    limit: 150,
  },
  {
    type: "OTHER",
    customName:
      "Gastos de locomoción y manutención del asegurado fuera de residencia",
    limit: 1500,
  },
  {
    type: "OTHER",
    customName: "1ª prótesis, gafas y aparatos ortopédicos",
    limit: 300,
  },
  {
    type: "OTHER",
    customName: "Desplazamiento y estancia de familiar (>100 km)",
    limit: 600,
  },
  {
    type: "PSYCHOLOGICAL_ASSISTANCE",
    description: "Asistencia psicológica al conductor",
    limit: 6000,
    limitMaxMonths: 6,
  },
  {
    type: "TRAVEL_ASSISTANCE",
    description: "Asistencia al vehículo y ocupantes (amplia)",
  },
  { 
    type: "TRAVEL_ASSISTANCE", 
    description: "Ayuda técnica en carretera" 
  },
  {
    type: "TRAVEL_ASSISTANCE",
    description: "Remolque en Península (sin límite de km)",
    limitIsUnlimited: true,
  },
  {
    type: "TRAVEL_ASSISTANCE",
    description: "Remolque fuera de territorio peninsular español (150 km)",
  },
  { 
    type: "RESCUE_EXPENSES", 
    description: "Gastos de rescate", 
    limit: 1000 
  },
  { 
    type: "TRAVEL_ASSISTANCE", 
    description: "Cambio de ruedas" 
  },
  { 
    type: "TRAVEL_ASSISTANCE", 
    description: "Envío de combustible" 
  },
  {
    type: "TRAVEL_ASSISTANCE",
    description: "Transporte o repatriación del vehículo (150 km)",
  },
  {
    type: "TRAVEL_ASSISTANCE",
    description: "Gastos de custodia del vehículo",
    limit: 300,
  },
  { type: "TRAVEL_ASSISTANCE", description: "Envío de piezas de recambio" },
  { type: "LOST_KEYS", description: "Extravío o robo de llaves", limit: 100 },
  {
    type: "IMMOBILIZATION",
    description: "Prestaciones por inmovilización (avería/accidente)",
  },
  {
    type: "THEFT",
    description: "Prestaciones a asegurados en caso de robo del vehículo",
  },
  {
    type: "TRAVEL_ASSISTANCE",
    description: "Traslado para recogida del vehículo reparado",
  },
  { type: "TRAVEL_ASSISTANCE", description: "Envío de chófer profesional" },
  {
    type: "LEGAL_DEFENSE",
    description: "Defensa jurídica automovilística en el extranjero",
    limit: 1200,
  },
  {
    type: "LEGAL_DEFENSE",
    description: "Fianzas penales en el extranjero",
    limit: 5000,
  },
  {
    type: "TRAVEL_ASSISTANCE",
    description: "Asistencia a las personas (general)",
  },
  {
    type: "TRAVEL_ASSISTANCE",
    description: "Transporte o repatriación sanitaria de heridos y enfermos",
  },
  {
    type: "TRAVEL_ASSISTANCE",
    description: "Transporte o repatriación de asegurados",
  },
  {
    type: "TRAVEL_ASSISTANCE",
    description: "Transporte o repatriación de menores",
  },
  {
    type: "HOTEL_EXPENSES_TRAVEL_ASSIST",
    description: "Desplazamiento de un familiar por hospitalización (España)",
    limitPerDay: 90,
    limitMaxDays: 10,
  },
  {
    type: "HOTEL_EXPENSES_TRAVEL_ASSIST",
    description:
      "Desplazamiento de un familiar por hospitalización (extranjero)",
    limitPerDay: 120,
    limitMaxDays: 10,
  },
  {
    type: "TRAVEL_ASSISTANCE",
    description:
      "Gastos médicos/quirúrgicos/farmacéuticos/hospitalarios en el extranjero",
    limit: 9000,
  },
  {
    type: "TRAVEL_ASSISTANCE",
    description: "Gastos odontológicos de urgencia en el extranjero",
    limit: 300,
  },
  {
    type: "REPATRIATION",
    description: "Transporte o repatriación de fallecidos y acompañantes",
    limit: 1000,
  },
  {
    type: "TRAVEL_ASSISTANCE",
    description: "Regreso anticipado por fallecimiento de familiar",
  },
  {
    type: "TRAVEL_ASSISTANCE",
    description: "Búsqueda y transporte de equipajes y efectos personales",
  },
  { type: "TRAVEL_ASSISTANCE", description: "Envío de medicamentos" },
  {
    type: "TRAVEL_ASSISTANCE",
    description: "Servicio de información para viajes al extranjero",
  },
  {
    type: "ADVANCE_COMPENSATION",
    description: "Adelanto de fondos en el extranjero",
    limit: 1000,
  },
  {
    type: "THIRD_PARTY_INSOLVENCY",
    description: "Insolvencia de terceros",
    limit: 8000,
  },
  {
    type: "MANDATORY_LIABILITY",
    description: "Responsabilidad civil obligatoria",
    liabilityBodilyCap: 70000000,
    liabilityPropertyCap: 15000000,
  },
  {
    type: "VOLUNTARY_LIABILITY",
    description: "RC complementaria",
    limit: 50000000,
  },
  {
    type: "VOLUNTARY_LIABILITY",
    description: "RC del conductor/tomador como peatón y ciclista",
    limit: 100000,
  },
  {
    type: "VOLUNTARY_LIABILITY",
    description:
      "RC por incendio o explosión del vehículo asegurado (estacionado)",
    limit: 100000,
  },

  {
    type: "LEGAL_DEFENSE",
    description: "Protección jurídica (límite por siniestro)",
    limit: 3000,
  },
  {
    type: "LEGAL_DEFENSE",
    description: "Defensa judicial (límite por siniestro)",
    limit: 3000,
  },
  {
    type: "LEGAL_DEFENSE",
    description: "Reclamación de daños (límite por siniestro)",
    limit: 3000,
  },
  {
    type: "FINES_MANAGEMENT",
    description: "Defensa de multas (gestión administrativa)",
  },

  {
    type: "ADVANCE_COMPENSATION",
    description:
      "Adelanto del importe de reparación del vehículo por daños materiales",
    limit: 10000,
  },
  { type: "LEGAL_DEFENSE", description: "Asesoría jurídica telefónica" },

  {
    type: "GLASS_BREAKAGE",
    description: "Rotura de lunas (incluida)",
    limitIsFullCost: true,
  },
  { type: "FIRE", description: "Incendio (incluida)" },
  { type: "THEFT", description: "Robo (incluida)" },

  {
    type: "OTHER",
    customName: "Reparaciones urgentes",
    description: "Reparaciones urgentes por siniestro",
    limit: 200,
  },
  {
    type: "NEW_VALUE_COMPENSATION",
    description:
      "100% valor nuevo < 2 años (nuevo) o valor compra < 2 años (usado); 80% año 3; resto valor de mercado",
    limitMaxMonths: 24,
  },

  {
    type: "DRIVER_ACCIDENTS",
    description: "Capital en caso de muerte",
    limit: 6000,
  },
  {
    type: "DRIVER_ACCIDENTS",
    description: "Capital en caso de invalidez",
    limit: 12000,
  },
  {
    type: "DRIVER_ACCIDENTS",
    description: "Asistencia sanitaria en centros concertados (ilimitada)",
    limitIsUnlimited: true,
  },
  {
    type: "DRIVER_ACCIDENTS",
    description: "Asistencia sanitaria en libre elección",
    limit: 1500,
  },
  {
    type: "ASSISTIVE_EQUIPMENT_RENTAL",
    description: "Alquiler de sillas de ruedas o muletas",
    limit: 150,
  },
  {
    type: "OTHER",
    customName:
      "Gastos de locomoción y manutención del asegurado fuera de residencia",
    limit: 1500,
  },
  {
    type: "OTHER",
    customName: "1ª prótesis, gafas y aparatos ortopédicos",
    limit: 300,
  },
  {
    type: "PSYCHOLOGICAL_ASSISTANCE",
    description: "Asistencia psicológica al conductor",
    limit: 6000,
    limitMaxMonths: 6,
  },

  {
    type: "TRAVEL_ASSISTANCE",
    description: "Asistencia al vehículo y ocupantes (amplia)",
  },
  { type: "TRAVEL_ASSISTANCE", description: "Ayuda técnica en carretera" },
  {
    type: "TRAVEL_ASSISTANCE",
    description: "Remolque en Península (sin límite de km)",
    limitIsUnlimited: true,
  },
  {
    type: "TRAVEL_ASSISTANCE",
    description: "Remolque fuera de territorio peninsular español (150 km)",
  },
  { type: "RESCUE_EXPENSES", description: "Gastos de rescate", limit: 1000 },
  { type: "TRAVEL_ASSISTANCE", description: "Cambio de ruedas" },
  { type: "TRAVEL_ASSISTANCE", description: "Envío de combustible" },
  {
    type: "TRAVEL_ASSISTANCE",
    description: "Transporte o repatriación del vehículo (150 km)",
  },
  {
    type: "TRAVEL_ASSISTANCE",
    description: "Gastos de custodia del vehículo",
    limit: 300,
  },
  { type: "TRAVEL_ASSISTANCE", description: "Envío de piezas de recambio" },
  { type: "LOST_KEYS", description: "Extravío o robo de llaves", limit: 100 },
  {
    type: "IMMOBILIZATION",
    description: "Prestaciones por inmovilización (avería/accidente)",
  },
  {
    type: "THEFT",
    description: "Prestaciones a asegurados en caso de robo del vehículo",
  },
  {
    type: "TRAVEL_ASSISTANCE",
    description: "Traslado para recogida del vehículo reparado",
  },
  { type: "TRAVEL_ASSISTANCE", description: "Envío de chófer profesional" },

  {
    type: "LEGAL_DEFENSE",
    description: "Defensa jurídica automovilística en el extranjero",
    limit: 1200,
  },
  {
    type: "LEGAL_DEFENSE",
    description: "Fianzas penales en el extranjero",
    limit: 5000,
  },

  {
    type: "TRAVEL_ASSISTANCE",
    description: "Asistencia a las personas (general)",
  },
  {
    type: "TRAVEL_ASSISTANCE",
    description: "Transporte o repatriación sanitaria de heridos y enfermos",
  },
  {
    type: "TRAVEL_ASSISTANCE",
    description: "Transporte o repatriación de asegurados",
  },
  {
    type: "TRAVEL_ASSISTANCE",
    description: "Transporte o repatriación de menores",
  },
  {
    type: "HOTEL_EXPENSES_TRAVEL_ASSIST",
    description: "Desplazamiento de un familiar por hospitalización (España)",
    limitPerDay: 90,
    limitMaxDays: 10,
  },
  {
    type: "HOTEL_EXPENSES_TRAVEL_ASSIST",
    description:
      "Desplazamiento de un familiar por hospitalización (extranjero)",
    limitPerDay: 120,
    limitMaxDays: 10,
  },
  {
    type: "TRAVEL_ASSISTANCE",
    description:
      "Gastos médicos/quirúrgicos/farmacéuticos/hospitalarios en el extranjero",
    limit: 9000,
  },
  {
    type: "TRAVEL_ASSISTANCE",
    description: "Gastos odontológicos de urgencia en el extranjero",
    limit: 300,
  },
  {
    type: "REPATRIATION",
    description: "Transporte o repatriación de fallecidos y acompañantes",
    limit: 1000,
  },
  {
    type: "TRAVEL_ASSISTANCE",
    description: "Regreso anticipado por fallecimiento de familiar",
  },
  {
    type: "TRAVEL_ASSISTANCE",
    description: "Búsqueda y transporte de equipajes y efectos personales",
  },
  { type: "TRAVEL_ASSISTANCE", description: "Envío de medicamentos" },
  {
    type: "TRAVEL_ASSISTANCE",
    description: "Servicio de información para viajes al extranjero",
  },
  {
    type: "ADVANCE_COMPENSATION",
    description: "Adelanto de fondos en el extranjero",
    limit: 1000,
  },
  {
    type: "THIRD_PARTY_INSOLVENCY",
    description: "Insolvencia de terceros",
    limit: 8000,
  },
];
