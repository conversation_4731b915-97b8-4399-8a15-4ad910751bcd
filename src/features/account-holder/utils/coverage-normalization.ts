import { Coverage, BidCoverage, GuaranteeType } from "@prisma/client";
import { formatCurrency } from "@/lib/utils";
import { translateGuaranteeType } from "@/features/policies/utils/translations";

// Types
export interface CoverageComparisonResult {
  guaranteeType: GuaranteeType;
  present: boolean;
  title: string;
  limit: string;
  description: string;
}

// Utility functions
export function isBidCoverageDataAvailable(bidCoverages: BidCoverage[]): boolean {
  return bidCoverages && bidCoverages.length > 0;
}

export function compareCoverages(
  policyCoverages: Coverage[],
  bidCoverages: BidCoverage[]
): CoverageComparisonResult[] {
  const results: CoverageComparisonResult[] = [];
  
  // Get all unique guarantee types from both policy and bid coverages
  const allGuaranteeTypes = new Set([
    ...policyCoverages.map(c => c.type),
    ...bidCoverages.map(c => c.type)
  ]);
  
  allGuaranteeTypes.forEach(guaranteeType => {
    const policyCoverage = policyCoverages.find(c => c.type === guaranteeType);
    const bidCoverage = bidCoverages.find(c => c.type === guaranteeType);
    
    results.push({
      guaranteeType,
      present: !!bidCoverage,
      title: translateGuaranteeType(guaranteeType),
      limit: bidCoverage ? formatCoverageLimit(bidCoverage) : (policyCoverage ? formatCoverageLimit(policyCoverage) : "No disponible"),
      description: bidCoverage?.description || policyCoverage?.description || ""
    });
  });
  
  return results;
}

export function getKeyCoverageDifferences(
  policyCoverages: Coverage[],
  bidCoverages: BidCoverage[]
): string[] {
  const differences: string[] = [];
  
  // Check for missing mandatory coverages
  const mandatoryCoverages = [GuaranteeType.MANDATORY_LIABILITY];
  mandatoryCoverages.forEach(guaranteeType => {
    const hasPolicyCoverage = policyCoverages.some(c => c.type === guaranteeType);
    const hasBidCoverage = bidCoverages.some(c => c.type === guaranteeType);
    
    if (hasPolicyCoverage && !hasBidCoverage) {
      differences.push(`Falta cobertura obligatoria: ${translateGuaranteeType(guaranteeType)}`);
    }
  });
  
  return differences;
}

export function formatCoverageLimit(coverage: Coverage | BidCoverage): string {
  if (coverage.limitIsUnlimited) {
    return "Ilimitado";
  }
  
  if (coverage.limitIsFullCost) {
    return "Coste completo";
  }
  
  if (coverage.limitPerDay && coverage.limitMaxDays) {
    return `${formatCurrency(Number(coverage.limitPerDay))}/día (máx. ${coverage.limitMaxDays} días)`;
  }
  
  if (coverage.limitPerDay && coverage.limitMaxMonths) {
    return `${formatCurrency(Number(coverage.limitPerDay))}/día (máx. ${coverage.limitMaxMonths} meses)`;
  }
  
  if (coverage.liabilityBodilyCap && coverage.liabilityPropertyCap) {
    return `${formatCurrency(Number(coverage.liabilityBodilyCap))} personas / ${formatCurrency(Number(coverage.liabilityPropertyCap))} daños`;
  }
  
  if (coverage.limit) {
    return formatCurrency(Number(coverage.limit));
  }
  
  return "No especificado";
}

export function calculateCoverageDifference(
  policyCoverage: Coverage | null,
  bidCoverage: BidCoverage | null
): { type: 'better' | 'worse' | 'same' | 'missing'; notes: string } {
  if (!bidCoverage) {
    return { type: 'missing', notes: 'Cobertura no incluida' };
  }
  
  if (!policyCoverage) {
    return { type: 'better', notes: 'Nueva cobertura añadida' };
  }
  
  // Compare limits
  if (bidCoverage.limitIsUnlimited && !policyCoverage.limitIsUnlimited) {
    return { type: 'better', notes: 'Límite mejorado a ilimitado' };
  }
  
  if (!bidCoverage.limitIsUnlimited && policyCoverage.limitIsUnlimited) {
    return { type: 'worse', notes: 'Límite reducido desde ilimitado' };
  }
  
  if (bidCoverage.limit && policyCoverage.limit) {
    if (Number(bidCoverage.limit) > Number(policyCoverage.limit)) {
      return { type: 'better', notes: 'Límite aumentado' };
    } else if (Number(bidCoverage.limit) < Number(policyCoverage.limit)) {
      return { type: 'worse', notes: 'Límite reducido' };
    }
  }
  
  return { type: 'same', notes: 'Sin cambios significativos' };
}

// Re-export centralized translation function for backward compatibility
export { translateGuaranteeType };

// React components moved to @/components/shared/ComparisonIndicator.tsx