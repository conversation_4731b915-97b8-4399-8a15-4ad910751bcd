"use client";

import { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Separator } from "@/components/ui/separator";
import { SidebarTrigger } from "@/components/ui/sidebar";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { 
  FileText, 
  Scale,
  XCircle,
  ArrowLeft,
  Clock,
  Check,
  Shield,
  Gavel,
  PiggyBank,
  Activity,
  ChevronUp,
  ChevronDown
} from "lucide-react";
import { 
  Timeline, 
  TimelineItem
} from "@/components/ui/timeline";
import { CollapsibleTimelineItem, BidDetail } from "@/components/ui/collapsible-timeline-item";
import { DetailsCard } from "@/components/ui/details-card";
import { PolicyDetailsDrawer } from "@/components/shared/PolicyDetailsDrawer";
import { PolicyData, BidComparisonData } from "@/types/policy";
import { PolicyStatus } from "@prisma/client";
import { formatCurrency, getAssetTypeIcon, formatDate } from "@/lib/utils";
import { formatAssetType } from "@/lib/format-asset-type";
import { formatInsurerCompany } from "@/lib/format-insurer";
import { formatTimeRemaining } from "@/features/auctions/utils/time-formatting";
import {
  translateFuelType,
  translateUsageType,
  translateGarageType,
  translateKmRange,
  translateGuaranteeType
} from "@/features/policies/utils/translations";

// Utility function to mask broker names for privacy
function maskBrokerName(fullName: string): string {
  if (!fullName || typeof fullName !== 'string') {
    return fullName || '';
  }
  
  const nameParts = fullName.trim().split(' ');
  if (!nameParts || nameParts.length === 0) {
    return fullName;
  }
  
  const firstName = nameParts[0];
  if (!firstName || nameParts.length === 1) {
    return firstName || '';
  }
  
  const lastName = nameParts[nameParts.length - 1];
  const lastNameInitial = lastName?.[0]?.toUpperCase();
  if (!lastNameInitial) {
    return firstName;
  }
  
  return `${firstName} ${lastNameInitial}.`;
}

// Transform API policy data to PolicyData interface
function transformApiPolicyToDrawerData(apiPolicy: AuctionDetails['policy']): PolicyData | undefined {
  if (!apiPolicy) return undefined;

  const vehicleDetails = apiPolicy.asset?.vehicleDetails;
  const primaryInsured = apiPolicy.insuredParties?.[0]; // Get first insured party as primary

  return {
    id: apiPolicy.id,
    policyNumber: apiPolicy.policyNumber || "",
    status: 'ACTIVE' as any, // Policies in auctions are typically active
    insurer: apiPolicy.insurerCompany ? formatInsurerCompany(apiPolicy.insurerCompany as any) : "",
    product: apiPolicy.productName || "Seguro de Vehículo", // Use dynamic product name or fallback
    policyType: apiPolicy.asset?.assetType ? formatAssetType(apiPolicy.asset.assetType) : "",
    validity: apiPolicy.endDate ? `Hasta ${formatDate(apiPolicy.endDate)}` : 'Fecha no disponible',
    annualPremium: formatCurrency(apiPolicy.premium || 0),
    holderName: primaryInsured?.fullName || "No disponible",
    birthDate: primaryInsured?.birthDate ? formatDate(primaryInsured.birthDate) : "No disponible",
    gender: primaryInsured?.gender || "No disponible",
    phone: primaryInsured?.phone || "No disponible",
    email: primaryInsured?.email || "No disponible",
    // Vehicle data mapping from database structure with proper type conversion and translation
    vehiclePlate: vehicleDetails?.licensePlate || "No disponible",
    vehicleFirstRegistrationDate: vehicleDetails?.firstRegistrationDate
      ? (() => {
          try {
            const date = new Date(vehicleDetails.firstRegistrationDate);
            return isNaN(date.getTime()) ? "No disponible" : date.toLocaleDateString('es-ES');
          } catch {
            return "No disponible";
          }
        })()
      : "No disponible",
    vehicleBrand: vehicleDetails?.brand || "No disponible",
    vehicleModel: vehicleDetails?.model || "No disponible",
    vehicleVersion: vehicleDetails?.version || "No disponible",
    vehicleManufacturingYear: vehicleDetails?.year?.toString() || "No disponible",
    vehicleType: apiPolicy.asset?.assetType ? formatAssetType(apiPolicy.asset.assetType) : "No disponible",
    vehicleFuelType: vehicleDetails?.fuelType
      ? translateFuelType(vehicleDetails.fuelType)
      : "No disponible",
    vehicleVin: vehicleDetails?.chassisNumber || "No disponible",
    vehiclePower: vehicleDetails?.powerCv?.toString() || "No disponible",
    vehicleSeats: vehicleDetails?.seats?.toString() || "No disponible",
    vehicleUsageType: vehicleDetails?.usageType
      ? translateUsageType(vehicleDetails.usageType)
      : "No disponible",
    vehicleGarageType: vehicleDetails?.garageType
      ? translateGarageType(vehicleDetails.garageType)
      : "No disponible",
    vehicleKmPerYear: vehicleDetails?.kmPerYear
      ? translateKmRange(vehicleDetails.kmPerYear.toString())
      : "No disponible",
    vehicleIsLeased: vehicleDetails?.isLeased === true ? "Sí" : "No",
    coverages: apiPolicy.coverages ? apiPolicy.coverages.map(c => ({
      title: c.customName || translateGuaranteeType(c.guaranteeType),
      limit: c.limit,
      description: c.description || '',
      guaranteeType: c.guaranteeType,
      severity: 'Medium'
    })) : [],
    // Add document property if available
    document: (apiPolicy as any).document || null,
  };
}

interface AuctionBid {
  id: string;
  annualPremium: number;
  brokerName: string;
  brokerCompany: string;
  createdAt: string;
  hasDocument: boolean;
  bidCoverages?: any[];
}

interface TimelineEvent {
  id: string;
  title: string;
  time: string;
  status: 'completed' | 'pending';
  bidDetails?: BidDetail[];
}

interface AuctionDetails {
  id: string;
  identifier: string;
  status: string;
  startDate: string | null;
  endDate: string | null;
  annualPremium: number;
  currency: string;
  currentInsurer: string | null;
  assetDisplayName: string;
  assetType: string | null;
  quotesReceived: number;
  bids: AuctionBid[];
  policy: {
    id: string;
    policyNumber: string;
    insurerCompany: string;
    premium: number;
    startDate: string | null;
    endDate: string | null;
    productName: string | null;
    asset: {
      id: string;
      assetType: string;
      description: string;
      vehicleDetails: {
        brand: string;
        model: string;
        year: number;
        licensePlate: string;
        chassisNumber: string;
        firstRegistrationDate: string | null;
        version: string | null;
        fuelType: string | null;
        powerCv: number | null;
        seats: number | null;
        usageType: string | null;
        garageType: string | null;
        kmPerYear: number | null;
        isLeased: boolean | null;
      } | null;
    } | null;
    insuredParties: {
      id: string;
      fullName: string;
      firstName: string;
      lastName: string;
      identification: string;
      roles: string[];
      gender: string;
      email: string;
      phone: string;
      birthDate: string | null;
      address: string;
      postalCode: string;
      regionName: string;
      country: string;
    }[];
    coverages: any[];
    document?: {
      id: string;
      fileName: string | null;
      fileSize: number | null;
      mimeType: string | null;
      url: string;
      uploadedAt: string;
    } | null;
  } | null; // Policy data from API
  events: TimelineEvent[];
}

interface AuctionDetailsViewProps {
  auctionId: string;
}

export function AuctionDetailsView({ auctionId }: AuctionDetailsViewProps) {
  const router = useRouter();
  const [auction, setAuction] = useState<AuctionDetails | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [isPolicyDrawerOpen, setIsPolicyDrawerOpen] = useState(false);
  const [isScrolled, setIsScrolled] = useState(false);
  const [timeRemaining, setTimeRemaining] = useState("Calculando...");
  const [comparisonBid, setComparisonBid] = useState<AuctionBid | null>(null);
  
  // Sorting and pagination state for bids table
  const [sortField, setSortField] = useState<'annualPremium' | 'createdAt' | 'brokerCompany'>('annualPremium');
  const [sortDirection, setSortDirection] = useState<'asc' | 'desc'>('asc');
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage, setItemsPerPage] = useState(5);

  useEffect(() => {
    if (auction?.endDate) {
      const updateTimeRemaining = () => {
        const formattedTime = formatTimeRemaining(auction.endDate!);
        setTimeRemaining(formattedTime);
      };

      // Update immediately
      updateTimeRemaining();

      // Update every minute (working hours don't need second-level precision)
      const interval = setInterval(updateTimeRemaining, 60000);

      return () => clearInterval(interval);
    }
  }, [auction?.endDate]);

  useEffect(() => {
    const handleScroll = () => {
      setIsScrolled(window.scrollY > 0);
    };

    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);

  useEffect(() => {
    const fetchAuctionDetails = async () => {
      try {
        const response = await fetch(`/api/account-holder/auctions/${auctionId}`);
        if (!response.ok) {
          throw new Error("Error al cargar los detalles de la subasta");
        }
        const data = await response.json();
        setAuction(data);
      } catch (err) {
        setError(err instanceof Error ? err.message : "Error desconocido");
      } finally {
        setLoading(false);
      }
    };

    fetchAuctionDetails();
  }, [auctionId]);

  const handleCompareClick = (bid: AuctionBid) => {
    setComparisonBid(bid);
    setIsPolicyDrawerOpen(true);
  };

  const transformBidToComparisonData = (bid: AuctionBid): BidComparisonData => {
    return {
      id: bid.id,
      annualPremium: bid.annualPremium,
      brokerName: bid.brokerName,
      brokerCompany: bid.brokerCompany,
      createdAt: bid.createdAt,
      hasDocument: bid.hasDocument,
      bidCoverages: bid.bidCoverages
    };
  };

  const formatDate = (dateString: string | null) => {
    if (!dateString) return "No especificada";
    return new Date(dateString).toLocaleDateString("es-ES", {
      year: "numeric",
      month: "long",
      day: "numeric",
      hour: "2-digit",
      minute: "2-digit",
    });
  };

  // Sorting and pagination logic for bids
  const handleSort = (field: 'annualPremium' | 'createdAt' | 'brokerCompany') => {
    if (sortField === field) {
      setSortDirection(sortDirection === 'asc' ? 'desc' : 'asc');
    } else {
      setSortField(field);
      setSortDirection('asc');
    }
    setCurrentPage(1); // Reset to first page when sorting
  };

  const getSortedBids = () => {
    if (!auction?.bids) return [];
    
    const sorted = [...auction.bids].sort((a, b) => {
      let aValue: any;
      let bValue: any;
      
      switch (sortField) {
        case 'annualPremium':
          aValue = a.annualPremium;
          bValue = b.annualPremium;
          break;
        case 'createdAt':
          aValue = new Date(a.createdAt).getTime();
          bValue = new Date(b.createdAt).getTime();
          break;
        case 'brokerCompany':
          aValue = a.brokerCompany.toLowerCase();
          bValue = b.brokerCompany.toLowerCase();
          break;
        default:
          return 0;
      }
      
      if (sortDirection === 'asc') {
        return aValue < bValue ? -1 : aValue > bValue ? 1 : 0;
      } else {
        return aValue > bValue ? -1 : aValue < bValue ? 1 : 0;
      }
    });
    
    return sorted;
  };

  const getPaginatedBids = () => {
    const sortedBids = getSortedBids();
    const startIndex = (currentPage - 1) * itemsPerPage;
    const endIndex = startIndex + itemsPerPage;
    return sortedBids.slice(startIndex, endIndex);
  };

  const getTotalPages = () => {
    const totalBids = auction?.bids?.length || 0;
    return Math.ceil(totalBids / itemsPerPage);
  };

  const handlePageChange = (page: number) => {
    setCurrentPage(page);
  };

  const handleItemsPerPageChange = (value: string) => {
    setItemsPerPage(Number(value));
    setCurrentPage(1);
  };

  const SortableHeader = ({ field, children }: { field: 'annualPremium' | 'createdAt' | 'brokerCompany'; children: React.ReactNode }) => {
    const isActive = sortField === field;
    const isAsc = sortDirection === 'asc';
    
    return (
      <TableHead 
        className="cursor-pointer hover:bg-gray-50 select-none"
        onClick={() => handleSort(field)}
      >
        <div className="flex items-center gap-1">
          {children}
          <div className="flex flex-col">
            <ChevronUp 
              className={`h-3 w-3 ${isActive && isAsc ? 'text-primary' : 'text-gray-400'}`} 
            />
            <ChevronDown 
              className={`h-3 w-3 -mt-1 ${isActive && !isAsc ? 'text-primary' : 'text-gray-400'}`} 
            />
          </div>
        </div>
      </TableHead>
    );
  };

  // Reset pagination when auction changes
  useEffect(() => {
    setCurrentPage(1);
  }, [auctionId]);

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4"></div>
          <p className="text-muted-foreground">Cargando detalles de la subasta...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <div className="text-center">
          <XCircle className="h-12 w-12 text-destructive mx-auto mb-4" />
          <h3 className="text-lg font-semibold mb-2">Error al cargar la subasta</h3>
          <p className="text-muted-foreground">{error}</p>
        </div>
      </div>
    );
  }

  if (!auction) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <div className="text-center">
          <FileText className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
          <h3 className="text-lg font-semibold mb-2">Subasta no encontrada</h3>
          <p className="text-muted-foreground">No se pudo encontrar la subasta solicitada.</p>
        </div>
      </div>
    );
  }

  return (
    <div className="flex flex-1 flex-col">
      {/* Sticky Header */}
      <div className={`sticky top-0 z-10 bg-white py-4 ${isScrolled ? 'shadow-md' : ''} transition-shadow duration-200`}>
        <div className="px-4">
          {/* Title with Sidebar Trigger and Back Button */}
          <div className="flex items-center gap-4 mb-4">
            <SidebarTrigger className="-ml-1 hover:bg-gray-100" />
            <Button
              variant="outline"
              size="icon"
              onClick={() => router.back()}
              className="h-8 w-8"
            >
              <ArrowLeft className="h-4 w-4" />
            </Button>
            <div className="flex items-center gap-3">
              <h1 className="text-xl font-semibold text-gray-900">
                {auction?.identifier || 'Cargando...'}
              </h1>
            </div>
          </div>
          <Separator className="mb-4" />
        </div>
      </div>

      {/* Summary Cards */}
      <div className="px-4 mb-6">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-4">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Tu póliza actual</CardTitle>
              <Shield className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">
                {formatCurrency(auction.annualPremium)}/año
              </div>
            </CardContent>
          </Card>
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Mejor Oferta</CardTitle>
              <Gavel className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">
                {auction.bids && auction.bids.length > 0
                  ? `${formatCurrency(Math.min(...auction.bids.map(bid => bid.annualPremium)))}/año`
                  : "Sin ofertas"}
              </div>
            </CardContent>
          </Card>
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              {auction.bids && auction.bids.length > 0 ? (() => {
                const bestOffer = Math.min(...auction.bids.map(bid => bid.annualPremium));
                const difference = auction.annualPremium - bestOffer;
                const isSaving = difference > 0;
                
                return (
                  <>
                    <CardTitle className="text-sm font-medium">
                      {isSaving ? "Ahorro Potencial" : "Coste Adicional"}
                    </CardTitle>
                    <PiggyBank className="h-4 w-4 text-muted-foreground" />
                  </>
                );
              })() : (
                <>
                  <CardTitle className="text-sm font-medium">Ahorro Potencial</CardTitle>
                  <PiggyBank className="h-4 w-4 text-muted-foreground" />
                </>
              )}
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">
                {auction.bids && auction.bids.length > 0 ? (() => {
                  const bestOffer = Math.min(...auction.bids.map(bid => bid.annualPremium));
                  const difference = auction.annualPremium - bestOffer;
                  const isSaving = difference > 0;
                  
                  return (
                    <span className={isSaving ? "text-green-600" : "text-red-600"}>
                      {formatCurrency(Math.abs(difference))}/año
                    </span>
                  );
                })() : "No disponible"}
              </div>
            </CardContent>
          </Card>
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Estado</CardTitle>
              <Activity className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <Badge variant={auction.status === 'OPEN' ? 'default' : 'destructive'}>{auction.status === 'OPEN' ? 'Abierta' : 'Cerrada'}</Badge>
            </CardContent>
          </Card>
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Tiempo Restante</CardTitle>
              <Clock className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">
                {timeRemaining}
              </div>
            </CardContent>
          </Card>
        </div>
      </div>

      {/* Content with Tabs */}
      <div className="flex-1 px-4 pb-4">
        <Tabs defaultValue="detalles" className="space-y-6">
          <TabsList className="grid w-full grid-cols-2">
            <TabsTrigger value="detalles">Detalles</TabsTrigger>
            <TabsTrigger value="ofertas">Ofertas Recibidas ({auction.bids?.length || 0})</TabsTrigger>
          </TabsList>

          <TabsContent value="detalles" className="mt-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="space-y-4">
                <DetailsCard
                  title={`Póliza: ${auction.policy?.policyNumber || 'No disponible'}`}
                  icon={<span className="text-2xl">{getAssetTypeIcon(auction.assetType || '')}</span>}
                  headerContent={<p className="text-sm text-muted-foreground">Póliza actual</p>}
                  action={
                    <Button 
                      variant="outline"
                      size="sm"
                      onClick={() => setIsPolicyDrawerOpen(true)}
                      disabled={!auction.policy}
                    >
                      Ver detalles
                    </Button>
                  }
                />

                <DetailsCard
                  title="¿Qué ocurre ahora?"
                >
                  <div className="space-y-4">
                    <div className="grid gap-4">
                      <div className="flex items-start gap-3">
                        <div className="w-2 h-2 bg-[#3AE386] rounded-full mt-2 flex-shrink-0"></div>
                        <div>
                          <p className="text-sm font-medium">Subasta activa</p>
                          <p className="text-sm text-muted-foreground">
                            Tu póliza ya está en subasta y los agentes están enviando sus ofertas.
                          </p>
                        </div>
                      </div>
                      
                      <div className="flex items-start gap-3">
                        <div className="w-2 h-2 bg-[#3AE386] rounded-full mt-2 flex-shrink-0"></div>
                        <div>
                          <p className="text-sm font-medium">Revisión y comparación</p>
                          <p className="text-sm text-muted-foreground">
                            Podrás ver y comparar cada oferta frente a tu póliza actual en cualquier momento.
                          </p>
                        </div>
                      </div>
                      
                      <div className="flex items-start gap-3">
                        <div className="w-2 h-2 bg-[#3AE386] rounded-full mt-2 flex-shrink-0"></div>
                        <div>
                          <p className="text-sm font-medium">Notificaciones por email</p>
                          <p className="text-sm text-muted-foreground">
                            Te avisaremos cada vez que recibas una nueva oferta.
                          </p>
                        </div>
                      </div>
                      
                      <div className="flex items-start gap-3">
                        <div className="w-2 h-2 bg-[#3AE386] rounded-full mt-2 flex-shrink-0"></div>
                        <div>
                          <p className="text-sm font-medium">Duración de la subasta</p>
                          <p className="text-sm text-muted-foreground">
                            La subasta dura 48 horas laborables (de lunes a viernes, de 06:00 a 23:59).
                          </p>
                        </div>
                      </div>
                      
                      <div className="flex items-start gap-3">
                        <div className="w-2 h-2 bg-[#3AE386] rounded-full mt-2 flex-shrink-0"></div>
                        <div>
                          <p className="text-sm font-medium">Próximos pasos</p>
                          <p className="text-sm text-muted-foreground">
                            Cuando finalice, se mostrarán los tres ganadores y podrás ponerte en contacto con ellos.
                          </p>
                        </div>
                      </div>
                    </div>

                    <div className="text-center pt-2">
                      <p className="font-medium text-[#3EA050]">
                        ⚡ No necesitas hacer nada más por ahora.
                      </p>
                      <p className="text-sm text-muted-foreground mt-2">
                        Nos encargamos de que recibas las mejores propuestas posibles.
                      </p>
                    </div>
                  </div>
                </DetailsCard>
              </div>

              <div className="space-y-6">
                <DetailsCard
                  title="Cronología de la subasta"
                >
                  <Timeline>
                    {auction.events.map((event, index) => {
                      // Use CollapsibleTimelineItem for events with bid details
                      if (event.bidDetails && event.bidDetails.length > 0) {
                        return (
                          <CollapsibleTimelineItem
                            key={event.id}
                            status={event.status}
                            title={event.title}
                            time={event.time}
                            bidDetails={event.bidDetails}
                            isLast={index === auction.events.length - 1}
                          />
                        );
                      }
                      
                      // Use regular TimelineItem for other events
                      return (
                        <TimelineItem 
                          key={event.id} 
                          status={event.status}
                          title={event.title}
                          time={event.time}
                          isLast={index === auction.events.length - 1}
                        />
                      );
                    })}
                  </Timeline>
                </DetailsCard>
              </div>
            </div>
          </TabsContent>

          <TabsContent value="ofertas" className="space-y-6">
            <DetailsCard
              title={`Ofertas Recibidas`}
            >
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>AGENTE</TableHead>
                    <SortableHeader field="brokerCompany">ASEGURADORA</SortableHeader>
                    <SortableHeader field="annualPremium">PRIMA</SortableHeader>
                    <SortableHeader field="createdAt">FECHA</SortableHeader>
                    <TableHead>ACCIÓN</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {auction.bids && auction.bids.length > 0 ? (
                    getPaginatedBids().map((bid) => (
                      <TableRow key={bid.id}>
                        <TableCell className="font-medium">{maskBrokerName(bid.brokerName)}</TableCell>
                        <TableCell>{bid.brokerCompany}</TableCell>
                        <TableCell className="text-green-600 font-semibold">
                          {formatCurrency(bid.annualPremium)}
                        </TableCell>
                        <TableCell>
                          {new Date(bid.createdAt).toLocaleDateString("es-ES")}
                        </TableCell>
                        <TableCell>
                          <Button 
                            variant="outline" 
                            size="sm"
                            onClick={() => handleCompareClick(bid)}
                          >
                            <Scale className="h-4 w-4 mr-1" />
                            Comparar
                          </Button>
                        </TableCell>
                      </TableRow>
                    ))
                  ) : (
                    <TableRow>
                      <TableCell colSpan={5} className="text-center py-8 text-gray-500">
                        No se han recibido ofertas aún
                      </TableCell>
                    </TableRow>
                  )}
                </TableBody>
              </Table>

              {/* Pagination Controls */}
              {auction.bids && auction.bids.length > 0 && (
                <div className="flex flex-col gap-3 sm:flex-row sm:items-center sm:justify-between sm:gap-4 pt-4">
                  <div className="flex items-center justify-center gap-1 sm:gap-2">
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => handlePageChange(currentPage - 1)}
                      disabled={currentPage === 1}
                      className="bg-primary text-black hover:bg-primary text-xs sm:text-sm px-2 sm:px-3"
                    >
                      Anterior
                    </Button>

                    <span className="text-xs sm:text-sm text-gray-600 whitespace-nowrap px-2">
                      Página {currentPage} de {getTotalPages()}
                    </span>

                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => handlePageChange(currentPage + 1)}
                      disabled={currentPage === getTotalPages()}
                      className="bg-primary text-black hover:bg-primary text-xs sm:text-sm px-2 sm:px-3"
                    >
                      Siguiente
                    </Button>
                  </div>

                  <div className="flex items-center justify-center gap-1 sm:gap-2">
                    <span className="text-xs sm:text-sm text-gray-600 whitespace-nowrap">Mostrar</span>
                    <Select
                      value={itemsPerPage.toString()}
                      onValueChange={handleItemsPerPageChange}
                    >
                      <SelectTrigger className="w-16 sm:w-20 text-center text-xs sm:text-sm">
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="5">5</SelectItem>
                        <SelectItem value="10">10</SelectItem>
                        <SelectItem value="20">20</SelectItem>
                      </SelectContent>
                    </Select>
                    <span className="text-xs sm:text-sm text-gray-600 whitespace-nowrap">elementos</span>
                  </div>
                </div>
              )}
            </DetailsCard>
          </TabsContent>
        </Tabs>
      </div>

      {/* Policy Details Drawer */}
      {auction.policy && (
        <PolicyDetailsDrawer
          isOpen={isPolicyDrawerOpen}
          onClose={() => {
            setIsPolicyDrawerOpen(false);
            setComparisonBid(null);
          }}
          mode={comparisonBid ? "comparison" : "account-holder"}
          policyData={transformApiPolicyToDrawerData(auction.policy)!}
          comparisonData={comparisonBid ? {
            bid: transformBidToComparisonData(comparisonBid),
            showComparison: true
          } : undefined}
        />
      )}
    </div>
  );
}