import { NextRequest, NextResponse } from "next/server";
import { getCurrentUser } from "@/features/auth/utils/server-auth";
import { db } from "@/lib/db";
import { z } from "zod";
import { transformPolicyInsuredPartiesData } from "@/features/policies/utils/insured-party-transformer";
import { normalizeForSearch } from "@/lib/text-utils";
import { formatInsurerCompany } from "@/lib/format-insurer";

// Validation schema for query parameters
const listPoliciesSchema = z.object({
  page: z.string().optional().default("1").transform(Number),
  limit: z.string().optional().default("10").transform(Number),
  status: z.string().optional(),
  assetType: z.string().optional(),
  search: z.string().optional(),
});

export async function GET(request: NextRequest) {
  try {
    // Get authenticated user using the proper server auth method
    const user = await getCurrentUser();

    if (!user) {
      return NextResponse.json(
        { error: "No autorizado" },
        { status: 401 }
      );
    }

    // Parse and validate query parameters
    const { searchParams } = new URL(request.url);
    const queryParams = Object.fromEntries(searchParams.entries());
    
    const validationResult = listPoliciesSchema.safeParse(queryParams);
    if (!validationResult.success) {
      return NextResponse.json(
        { error: "Parámetros de consulta inválidos", details: validationResult.error.errors },
        { status: 400 }
      );
    }

    const { page, limit, status, assetType, search } = validationResult.data;

    // Calculate pagination
    const skip = (page - 1) * limit;

    // First, get the account holder profile for this user
    let accountHolderProfile = await db.accountHolderProfile.findUnique({
      where: { userId: user.id }
    });

    // If profile doesn't exist, create it (fallback for missed trigger execution)
    if (!accountHolderProfile && user.role === "ACCOUNT_HOLDER") {
      try {
        accountHolderProfile = await db.accountHolderProfile.create({
          data: {
            userId: user.id
          }
        });
        console.log(`Created missing AccountHolderProfile for user ${user.id}`);
      } catch (error) {
        console.error(`Failed to create AccountHolderProfile for user ${user.id}:`, error);
        return NextResponse.json(
          { error: "Error al crear perfil de titular de cuenta" },
          { status: 500 }
        );
      }
    }

    if (!accountHolderProfile) {
      return NextResponse.json(
        { error: "Perfil de titular de cuenta no encontrado" },
        { status: 404 }
      );
    }

    // Build where clause for filtering
    const whereClause: any = {
      accountHolderId: accountHolderProfile.id, // Ensure RLS - only user's own policies
    };

    // Add status filter if provided
    if (status && status !== "all") {
      if (status === "attention") {
        whereClause.status = {
          in: ["RENEW_SOON", "EXPIRED"]
        };
      } else {
        whereClause.status = status;
      }
    }

    // Add asset type filter if provided
    if (assetType && assetType !== "all") {
      whereClause.asset = {
        assetType: assetType
      };
    }

    // Add search filter if provided
    if (search) {
      // Normalize search term for accent-insensitive comparison
      const normalizedSearch = normalizeForSearch(search);
      
      // Try to parse search as number for premium comparison
      const searchAsNumber = parseFloat(search);
      const isNumericSearch = !isNaN(searchAsNumber);

      whereClause.OR = [
        // Search in policy number
        {
          policyNumber: {
            contains: search,
            mode: "insensitive"
          }
        },
        // Search in product name
        {
          productName: {
            contains: search,
            mode: "insensitive"
          }
        },
        // Search in vehicle brand and model
        {
          asset: {
            vehicleDetails: {
              OR: [
                {
                  brand: {
                    contains: search,
                    mode: "insensitive"
                  }
                },
                {
                  model: {
                    contains: search,
                    mode: "insensitive"
                  }
                }
              ]
            }
          }
        },
        // Search in insured party names (accent-insensitive)
        {
          insuredParties: {
            some: {
              insuredParty: {
                OR: [
                  {
                    firstName: {
                      contains: search,
                      mode: "insensitive"
                    }
                  },
                  {
                    lastName: {
                      contains: search,
                      mode: "insensitive"
                    }
                  },
                  {
                    displayName: {
                      contains: search,
                      mode: "insensitive"
                    }
                  }
                ]
              }
            }
          }
        },
        // Search in insurer company (enum-based search with accent normalization)
        {
          insurerCompany: {
            in: Object.values({
              MAPFRE: "MAPFRE",
              ALLIANZ: "ALLIANZ",
              AXA: "AXA",
              GENERALI: "GENERALI",
              SANTALUCIA: "SANTALUCIA",
              MUTUA_MADRILENA: "MUTUA_MADRILENA",
              DIRECT_SEGUROS: "DIRECT_SEGUROS",
              LINEA_DIRECTA: "LINEA_DIRECTA",
              REALE_SEGUROS: "REALE_SEGUROS",
              ZURICH: "ZURICH",
              CATALANA_OCCIDENTE: "CATALANA_OCCIDENTE",
              DKV: "DKV",
              FIATC: "FIATC",
              HELVETIA: "HELVETIA",
              PLUS_ULTRA: "PLUS_ULTRA",
              AEGON: "AEGON",
              QUALITAS_AUTO: "QUALITAS_AUTO",
              BALOISE: "BALOISE",
              PELAYO: "PELAYO",
              MMT_SEGUROS: "MMT_SEGUROS",
              NATIONALE_NEDERLANDEN: "NATIONALE_NEDERLANDEN",
              LIBERTY_SEGUROS: "LIBERTY_SEGUROS",
              ADESLAS: "ADESLAS",
              ASISA: "ASISA",
              SANITAS: "SANITAS",
              CASER: "CASER",
              OCASO: "OCASO",
              ARAG: "ARAG",
              EUROP_ASSISTANCE: "EUROP_ASSISTANCE",
              INTERMUTUAS: "INTERMUTUAS",
              MGS_SEGUROS: "MGS_SEGUROS",
              SEGURCAIXA_ADESLAS: "SEGURCAIXA_ADESLAS",
              VERTI: "VERTI",
              GENESIS: "GENESIS",
              OTHER: "OTRAS"
            }).filter(company => normalizeForSearch(company).includes(normalizedSearch))
          }
        }
      ];

      // Add premium search if the search term is numeric
      if (isNumericSearch) {
        whereClause.OR.push(
          // Exact match
          {
            premium: {
              equals: searchAsNumber
            }
          },
          // Range match (within 1% tolerance for decimal precision)
          {
            premium: {
              gte: searchAsNumber * 0.99,
              lte: searchAsNumber * 1.01
            }
          }
        );
      }
    }

    // Query policies with relations
    const [policies, totalCount] = await Promise.all([
      db.policy.findMany({
        where: whereClause,
        include: {
          asset: {
            include: {
              vehicleDetails: {
                select: {
                  licensePlate: true,
                  firstRegistrationDate: true,
                  brand: true,
                  model: true,
                  version: true,
                  year: true,
                  fuelType: true,
                  chassisNumber: true,
                  powerCv: true,
                  seats: true,
                  usageType: true,
                  garageType: true,
                  kmPerYear: true,
                  isLeased: true
                }
              }
            }
          },
          accountHolder: {
            include: {
              user: {
                select: {
                  firstName: true,
                  lastName: true
                }
              }
            }
          },
          document: {
            select: {
              id: true,
              fileName: true,
              fileSize: true,
              mimeType: true,
              url: true,
              uploadedAt: true
            }
          },
          coverages: true,
          insuredParties: {
            include: {
              insuredParty: true
            }
          }
        },
        orderBy: {
          updatedAt: "desc"
        },
        skip,
        take: limit,
      }),
      db.policy.count({
        where: whereClause,
      })
    ]);

    // Apply additional accent-insensitive filtering if search is provided
    let filteredPolicies = policies;
    if (search) {
      const normalizedSearch = normalizeForSearch(search);
      const searchAsNumber = parseFloat(search);
      const isNumericSearch = !isNaN(searchAsNumber);
      
      filteredPolicies = policies.filter(policy => {
        // Check if any searchable field matches with accent normalization
        const searchableFields = [
           policy.policyNumber,
           policy.productName,
           policy.asset?.vehicleDetails?.brand,
           policy.asset?.vehicleDetails?.model,
           // Insured party names (focus only on these for accent-insensitive search)
           ...policy.insuredParties.map(ip => ip.insuredParty?.firstName).filter(Boolean),
           ...policy.insuredParties.map(ip => ip.insuredParty?.lastName).filter(Boolean),
           ...policy.insuredParties.map(ip => ip.insuredParty?.displayName).filter(Boolean),
           policy.insurerCompany
         ].filter(Boolean);

        // Check text fields with accent normalization
        const textMatch = searchableFields.some(field => 
          normalizeForSearch(field || '').includes(normalizedSearch)
        );

        // Check premium if search is numeric
        const premiumMatch = isNumericSearch && policy.premium && 
          (policy.premium.toString().includes(search) || 
           Math.abs(parseFloat(policy.premium.toString()) - searchAsNumber) < 0.01 ||
           // Also check formatted premium (e.g., "420.00" matches "420")
           policy.premium.toString().startsWith(search));

        return textMatch || premiumMatch;
      });
    }

    // Transform data for frontend
    const transformedPolicies = filteredPolicies.map((policy) => ({
      id: policy.id,
      policyNumber: policy.policyNumber,
      status: policy.status,
      type: policy.asset?.assetType || "UNKNOWN", // Add type field for filtering
      startDate: policy.startDate,
      endDate: policy.endDate,
      premium: policy.premium,
      productName: policy.productName,
      insurerCompany: policy.insurerCompany ? formatInsurerCompany(policy.insurerCompany) : null,
      asset: policy.asset ? {
        id: policy.asset.id,
        assetType: policy.asset.assetType,
        description: policy.asset.description,
        value: policy.asset.value,
        vehicleDetails: policy.asset.vehicleDetails ? {
          licensePlate: policy.asset.vehicleDetails.licensePlate,
          firstRegistrationDate: policy.asset.vehicleDetails.firstRegistrationDate,
          brand: policy.asset.vehicleDetails.brand,
          model: policy.asset.vehicleDetails.model,
          version: policy.asset.vehicleDetails.version,
          year: policy.asset.vehicleDetails.year,
          fuelType: policy.asset.vehicleDetails.fuelType,
          chassisNumber: policy.asset.vehicleDetails.chassisNumber,
          powerCv: policy.asset.vehicleDetails.powerCv,
          seats: policy.asset.vehicleDetails.seats,
          usageType: policy.asset.vehicleDetails.usageType,
          garageType: policy.asset.vehicleDetails.garageType,
          kmPerYear: policy.asset.vehicleDetails.kmPerYear,
          isLeased: policy.asset.vehicleDetails.isLeased
        } : null
      } : null,
      insuredParties: policy.insuredParties ? transformPolicyInsuredPartiesData(policy.insuredParties) : [],
      accountHolder: policy.accountHolder ? {
        id: policy.accountHolder.id,
        userId: policy.accountHolder.userId,
        firstName: policy.accountHolder.user.firstName,
        lastName: policy.accountHolder.user.lastName
      } : null,
      document: policy.document ? {
        id: policy.document.id,
        fileName: policy.document.fileName,
        fileSize: policy.document.fileSize,
        mimeType: policy.document.mimeType,
        url: policy.document.url,
        uploadedAt: policy.document.uploadedAt.toISOString()
      } : null,
      coverages: policy.coverages || []
    }));

    const totalPages = Math.ceil(totalCount / limit);
    const hasNextPage = page < totalPages;
    const hasPreviousPage = page > 1;

    return NextResponse.json({
      data: transformedPolicies,
      pagination: {
        page,
        limit,
        totalCount,
        totalPages,
        hasNextPage,
        hasPreviousPage,
      }
    });

  } catch (error) {
    console.error("Error fetching policies:", error);
    
    // Log more detailed error information
    if (error instanceof Error) {
      console.error("Error message:", error.message);
      console.error("Error stack:", error.stack);
    }
    
    return NextResponse.json(
      { 
        error: "Error interno del servidor",
        details: process.env.NODE_ENV === "development" ? error instanceof Error ? error.message : String(error) : undefined
      },
      { status: 500 }
    );
  }
}