#!/usr/bin/env tsx

/**
 * Validation script for sample coverage data
 * Ensures the sample data structure is compatible with Prisma schema and PolicyDetailsDrawer
 */

import { sampleCoverages } from '../docs/reports/sample-structered-coverages';

// Define the expected structure based on Prisma schema
interface ExpectedCoverageStructure {
  type: string;
  customName?: string | null;
  description?: string | null;
  limit?: number | null;
  limitIsUnlimited?: boolean;
  limitIsFullCost?: boolean;
  limitPerDay?: number | null;
  limitMaxDays?: number | null;
  limitMaxMonths?: number | null;
  liabilityBodilyCap?: number | null;
  liabilityPropertyCap?: number | null;
  deductible?: number | null;
  deductiblePercent?: number | null;
}

// Required fields for all coverage entries
const requiredFields = ['type', 'description'];

// Optional fields that should be present (even if null)
const expectedFields = [
  'type',
  'customName',
  'description',
  'limit',
  'limitIsUnlimited',
  'limitIsFullCost',
  'limitPerDay',
  'limitMaxDays',
  'limitMaxMonths',
  'liabilityBodilyCap',
  'liabilityPropertyCap',
  'deductible',
  'deductiblePercent'
];

function validateCoverageStructure(coverage: any, index: number): boolean {
  let isValid = true;
  const errors: string[] = [];

  // Check required fields
  for (const field of requiredFields) {
    if (!(field in coverage) || coverage[field] === undefined) {
      errors.push(`Missing required field: ${field}`);
      isValid = false;
    }
  }

  // Check expected fields presence
  for (const field of expectedFields) {
    if (!(field in coverage)) {
      errors.push(`Missing expected field: ${field}`);
      isValid = false;
    }
  }

  // Validate specific field types
  if (coverage.type && typeof coverage.type !== 'string') {
    errors.push(`Field 'type' must be a string`);
    isValid = false;
  }

  if (coverage.limitIsUnlimited !== undefined && typeof coverage.limitIsUnlimited !== 'boolean') {
    errors.push(`Field 'limitIsUnlimited' must be a boolean`);
    isValid = false;
  }

  if (coverage.limitIsFullCost !== undefined && typeof coverage.limitIsFullCost !== 'boolean') {
    errors.push(`Field 'limitIsFullCost' must be a boolean`);
    isValid = false;
  }

  // Validate numeric fields
  const numericFields = ['limit', 'limitPerDay', 'limitMaxDays', 'limitMaxMonths', 'liabilityBodilyCap', 'liabilityPropertyCap', 'deductible', 'deductiblePercent'];
  for (const field of numericFields) {
    if (coverage[field] !== null && coverage[field] !== undefined && typeof coverage[field] !== 'number') {
      errors.push(`Field '${field}' must be a number or null`);
      isValid = false;
    }
  }

  // Validate mandatory liability coverage
  if (coverage.type === 'MANDATORY_LIABILITY') {
    if (!coverage.liabilityBodilyCap || !coverage.liabilityPropertyCap) {
      errors.push(`MANDATORY_LIABILITY coverage must have liabilityBodilyCap and liabilityPropertyCap`);
      isValid = false;
    }
  }

  // Validate OTHER type coverage
  if (coverage.type === 'OTHER' && !coverage.customName) {
    errors.push(`OTHER type coverage must have a customName`);
    isValid = false;
  }

  if (!isValid) {
    console.error(`❌ Coverage ${index + 1} (${coverage.type || 'unknown'}) validation failed:`);
    errors.forEach(error => console.error(`   - ${error}`));
  }

  return isValid;
}

function validateSampleData(): boolean {
  console.log('🔍 Validating sample coverage data...\n');

  let allValid = true;
  let validCount = 0;
  let invalidCount = 0;

  // Validate each coverage entry
  sampleCoverages.forEach((coverage, index) => {
    const isValid = validateCoverageStructure(coverage, index);
    if (isValid) {
      validCount++;
      console.log(`✅ Coverage ${index + 1} (${coverage.type}): Valid`);
    } else {
      invalidCount++;
      allValid = false;
    }
  });

  // Summary
  console.log(`\n📊 Validation Summary:`);
  console.log(`   Total coverages: ${sampleCoverages.length}`);
  console.log(`   Valid: ${validCount}`);
  console.log(`   Invalid: ${invalidCount}`);

  if (allValid) {
    console.log(`\n🎉 All sample coverage data is valid and compatible with the schema!`);
  } else {
    console.log(`\n❌ Some coverage data entries need to be fixed.`);
  }

  // Check for duplicate coverage types
  const typeCount = new Map<string, number>();
  sampleCoverages.forEach(coverage => {
    const key = coverage.type + (coverage.customName || '');
    typeCount.set(key, (typeCount.get(key) || 0) + 1);
  });

  const duplicates = Array.from(typeCount.entries()).filter(([_, count]) => count > 1);
  if (duplicates.length > 0) {
    console.log(`\n⚠️  Duplicate coverage types found:`);
    duplicates.forEach(([type, count]) => {
      console.log(`   - ${type}: ${count} occurrences`);
    });
  }

  return allValid;
}

// Run validation
if (require.main === module) {
  const isValid = validateSampleData();
  process.exit(isValid ? 0 : 1);
}

export { validateSampleData };
